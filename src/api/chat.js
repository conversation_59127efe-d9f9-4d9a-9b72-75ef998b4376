import request from '@/utils/http'

/**
 * 聊天相关 API
 */

/**
 * 流式聊天 - 知识库对话
 * @param {Object} params 聊天参数
 * @param {string} params.query 用户问题
 * @param {string} params.knowledge_base_name 知识库名称
 * @param {Array} params.history 历史对话
 * @param {boolean} params.stream 是否流式输出
 * @param {Object} params.config 其他配置参数
 */
export function streamChatWithKnowledge(params) {
  const { query, knowledge_base_name, history = [], stream = true, ...config } = params

  const requestBody = {
    query,
    knowledge_base_name,
    stream,
    history,
    ...config
  }

  // 使用 uni.request 的 enableChunked 进行真正的流式请求
  return new Promise((resolve, reject) => {
    // 创建一个兼容 fetch API 的 Response 对象
    const mockResponse = {
      ok: true,
      status: 200,
      requestTask: null, // 用于存储请求任务
      body: {
        getReader() {
          let chunks = []
          let isFinished = false
          let currentIndex = 0
          let requestTask = null

          // 发起流式请求
          requestTask = uni.request({
            url: `${config.streamBaseURL || '/stream-api'}/chat/knowledge_base_chat`,
            method: 'POST',
            header: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${uni.getStorageSync('token')}`
            },
            data: requestBody,
            enableChunked: true, // 启用分块传输，这是关键
            success: (res) => {
              console.log('流式请求成功:', res.statusCode)
              if (res.statusCode === 200) {
                // 处理完整响应
                if (res.data) {
                  chunks.push(res.data)
                }
                isFinished = true
              } else {
                reject(new Error(`请求失败: ${res.statusCode}`))
              }
            },
            fail: (error) => {
              console.error('流式请求失败:', error)
              reject(error)
            }
          })

          // 将 requestTask 保存到 response 对象中
          mockResponse.requestTask = requestTask

          // 监听分块数据
          if (requestTask && requestTask.onChunkReceived) {
            requestTask.onChunkReceived((res) => {
              console.log('收到数据块:', res.data)
              if (res.data) {
                chunks.push(res.data)
              }
            })
          }

          return {
            async read() {
              // 等待有数据或请求完成
              while (currentIndex >= chunks.length && !isFinished) {
                await new Promise(resolve => setTimeout(resolve, 50))
              }

              // 如果有新的数据块
              if (currentIndex < chunks.length) {
                const chunk = chunks[currentIndex]
                currentIndex++
                return {
                  done: false,
                  value: new TextEncoder().encode(chunk)
                }
              }

              // 如果请求已完成且没有更多数据
              if (isFinished) {
                return { done: true, value: undefined }
              }

              // 继续等待
              return new Promise(resolve => {
                setTimeout(() => {
                  resolve(this.read())
                }, 50)
              })
            }
          }
        }
      }
    }

    resolve(mockResponse)
  })
}

/**
 * 普通聊天请求 - 知识库对话
 * @param {Object} data 聊天数据
 */
export function chatWithKnowledge(data) {
  return request({
    url: 'techDocManage/knowledge/answerResult',
    method: 'post',
    data: JSON.stringify(data),
    header: {
      'Content-Type': 'application/json'
    }
  })
}

/**
 * 获取知识库聊天记录
 * @param {Object} params 查询参数
 */
export function getKnowledgeChatList(params) {
  return request({
    url: 'techDocManage/knowledge/getKnowledgeChatByKnowledgeName',
    method: 'get',
    params
  })
}

/**
 * 获取知识库聊天数量统计
 * @param {Object} params 查询参数
 */
export function getKnowledgeChatNum(params) {
  return request({
    url: 'techDocManage/knowledge/getKnowledgeChatNum',
    method: 'get',
    params
  })
}

/**
 * 添加聊天记录
 * @param {Object} data 聊天数据
 */
export function addChat(data) {
  return request({
    url: 'techDocManage/knowledge/addChat',
    method: 'post',
    data
  })
}
