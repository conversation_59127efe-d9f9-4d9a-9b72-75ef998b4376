<script setup lang="ts">
interface History {
  id: string
  query?: string
  create_time: string
}

interface Props {
  show: boolean
  historyList: History[]
}

defineProps<Props>()

const emit = defineEmits<{
  close: []
  selectHistory: [history: History]
}>()

const handleClose = () => {
  emit('close')
}

const handleSelectHistory = (history: History) => {
  emit('selectHistory', history)
}

// 格式化时间
const formatTime = (timeStr: string) => {
  if (!timeStr) return ''
  const date = new Date(timeStr)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  if (diff < 24 * 60 * 60 * 1000) {
    // 今天
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    })
  } else if (diff < 7 * 24 * 60 * 60 * 1000) {
    // 一周内
    const days = Math.floor(diff / (24 * 60 * 60 * 1000))
    return `${days}天前`
  } else {
    // 超过一周
    return date.toLocaleDateString('zh-CN', {
      month: '2-digit',
      day: '2-digit'
    })
  }
}
</script>

<template>
  <view v-if="show" class="history-drawer" @click="handleClose">
    <view class="drawer-content" @click.stop>
      <view class="drawer-header">
        <text class="drawer-title">历史对话</text>
        <text class="close-btn" @click="handleClose">×</text>
      </view>
      <scroll-view class="history-list" scroll-y>
        <view
          v-for="history in historyList"
          :key="history.id"
          class="history-item"
          @click="handleSelectHistory(history)"
        >
          <view class="history-content">
            <text class="history-title">{{ history.query || '对话记录' }}</text>
            <text class="history-time">{{ formatTime(history.create_time) }}</text>
          </view>
        </view>
        <view v-if="historyList.length === 0" class="empty-history">
          <text class="empty-text">暂无历史对话</text>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<style scoped lang="scss">
@import '../styles/mixins.scss';

.history-drawer {
  @include drawer-style;
}

.drawer-content {
  @include drawer-content;
}

.drawer-header {
  @include header-style;
}

.drawer-title {
  font-size: 32rpx;
  font-weight: 500;
  color: $text-primary;
}

.close-btn {
  font-size: 48rpx;
  color: $text-secondary;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: $border-radius-circle;
  @include button-hover;
}

.history-list {
  flex: 1;
  padding: $spacing-md 0;
}

.history-item {
  @include list-item;
}

.history-content {
  display: flex;
  flex-direction: column;
  gap: $spacing-xs;
}

.history-title {
  font-size: 28rpx;
  color: $text-primary;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.history-time {
  font-size: 24rpx;
  color: $text-secondary;
}

.empty-history {
  height: 200rpx;
  @include empty-state;
}

.empty-text {
  font-size: 28rpx;
  color: $text-secondary;
}
</style>
