<script setup lang="ts">
import { ref } from 'vue'

interface Knowledge {
  label: string
  value: string
}

interface Props {
  knowledgeList: Knowledge[]
  currentKnowledge?: Knowledge
  isSending: boolean
  safeAreaBottom: number
}

defineProps<Props>()

const emit = defineEmits<{
  selectKnowledge: [knowledge: Knowledge]
  send: [text: string]
}>()

const inputText = ref('')

const handleSelectKnowledge = (knowledge: Knowledge) => {
  emit('selectKnowledge', knowledge)
}

const sendMessage = () => {
  if (!inputText.value.trim()) return

  const text = inputText.value.trim()
  inputText.value = ''
  emit('send', text)
}
</script>

<template>
  <view class="bottom-panel">
    <!-- 知识库选择选项卡 -->
    <view class="knowledge-tabs">
      <scroll-view 
        class="tabs-scroll" 
        scroll-x 
        :show-scrollbar="false"
        :enable-flex="true"
      >
        <view class="tabs-container">
          <view
            v-for="knowledge in knowledgeList"
            :key="knowledge.label"
            class="tab-item"
            :class="{ 'active': currentKnowledge?.label === knowledge.label }"
            @click="handleSelectKnowledge(knowledge)"
          >
            <text class="tab-icon">{{ knowledge.icon || '📚' }}</text>
            <text class="tab-text">{{ knowledge.label }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 输入区域 -->
    <view class="input-section">
      <view class="input-wrapper">
        <view class="input-container">
          <textarea
            v-model="inputText"
            class="input-field"
            placeholder="输入您的问题..."
            :disabled="isSending"
            auto-height
            :maxlength="1000"
            @confirm="sendMessage"
          />
        </view>
        <view class="action-buttons">
          <button
            class="send-button"
            :class="{ 'sending': isSending, 'disabled': !inputText.trim() }"
            @click="sendMessage"
            :disabled="isSending || !inputText.trim()"
          >
            <text class="send-icon">{{ isSending ? '⏸' : '→' }}</text>
          </button>
        </view>
      </view>
      <!-- 安全区域占位 -->
      <view class="safe-area" :style="{ height: safeAreaBottom + 'px' }"></view>
    </view>
  </view>
</template>

<style scoped lang="scss">
@import '../styles/variables.scss';

.bottom-panel {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: $input-area-bg;
  border-top: 1rpx solid $border-color;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.06);
  z-index: 100;
}

.knowledge-tabs {
  padding: $spacing-md 0;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.tabs-scroll {
  width: 100%;
  white-space: nowrap;
}

.tabs-container {
  display: flex;
  align-items: center;
  padding: 0 $spacing-md;
  gap: $spacing-md;
}

.tab-item {
  flex-shrink: 0;
  padding: 12rpx 20rpx;
  background-color: #F9FAFB;
  border: 2rpx solid #E5E7EB;
  border-radius: 24rpx;
  transition: all $transition-fast;
  min-width: 100rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.05);

  &:active {
    transform: scale(0.96);
  }

  &.active {
    background: linear-gradient(135deg, $accent-color 0%, #0d47d9 100%);
    border-color: $accent-color;
    box-shadow: 0 2rpx 8rpx rgba(20, 91, 255, 0.25);

    .tab-text {
      color: #FFFFFF;
      font-weight: 600;
    }

    .tab-icon {
      filter: brightness(1.2);
    }
  }
}

.tab-icon {
  font-size: 20rpx;
  line-height: 1;
}

.tab-text {
  font-size: 26rpx;
  color: #374151;
  white-space: nowrap;
  line-height: 1.3;
  font-weight: 500;
}

.input-section {
  padding: $spacing-md;
}

.input-wrapper {
  display: flex;
  align-items: center; /* 改为居中对齐 */
  gap: $spacing-sm;
  background-color: #FFFFFF;
  border: 2rpx solid #E5E7EB;
  border-radius: 28rpx;
  padding: $spacing-sm;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  transition: all $transition-fast;
  min-height: 80rpx; /* 确保有足够的高度 */

  &:focus-within {
    border-color: $accent-color;
    box-shadow: 0 2rpx 12rpx rgba(20, 91, 255, 0.15);
  }
}

.input-container {
  flex: 1;
  background: transparent;
  border: none;
  padding: $spacing-md;
  display: flex;
  align-items: center;
}

.input-field {
  width: 100%;
  min-height: 44rpx;
  max-height: 160rpx;
  font-size: 30rpx;
  color: $text-primary;
  background: transparent;
  border: none;
  outline: none;
  resize: none;
  line-height: 1.5;

  &::placeholder {
    color: #9CA3AF;
    font-size: 30rpx;
  }
}

.action-buttons {
  display: flex;
  align-items: center;
  flex-shrink: 0; /* 防止按钮被压缩 */
}

.send-button {
  width: 64rpx;
  height: 64rpx;
  background: linear-gradient(135deg, $accent-color 0%, #0d47d9 100%);
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all $transition-fast;
  box-shadow: 0 2rpx 8rpx rgba(20, 91, 255, 0.3);

  &:not(.disabled):active {
    transform: scale(0.95);
    box-shadow: 0 1rpx 4rpx rgba(20, 91, 255, 0.4);
  }

  &.disabled {
    background: #E5E7EB;
    box-shadow: none;

    .send-icon {
      color: #9CA3AF;
    }
  }

  &.sending {
    background: linear-gradient(135deg, #FF4757 0%, #FF3742 100%);
    animation: pulse 1.5s infinite;
  }
}

.send-icon {
  font-size: 28rpx;
  color: #FFFFFF;
  font-weight: 600;
}

.safe-area {
  background-color: $input-area-bg;
  min-height: 20rpx;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}
</style>
