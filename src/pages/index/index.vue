<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/store/user'

const userStore = useUserStore()

// 用户信息
const userInfo = ref({})

onMounted(() => {
  // 检查登录状态
  if (!userStore.isLoggedIn) {
    uni.reLaunch({
      url: '/pages/login/index'
    })
    return
  }

  // 检查用户状态
  userStore.checkAuthStatus()
  userInfo.value = userStore.userInfo
})

// 进入聊天
const goToChat = () => {
  uni.navigateTo({
    url: '/pages/chat/index'
  })
}

// 退出登录
const logout = () => {
  uni.showModal({
    title: '提示',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        userStore.logout()
      }
    }
  })
}
</script>

<template>
  <view class="container">
    <!-- 头部信息 -->
    <view class="header">
      <view class="user-info">
        <text class="welcome">欢迎使用</text>
        <text class="app-name">灵犀智问</text>
        <text class="user-name">{{ userInfo.userName || '用户' }}</text>
      </view>
      <view class="logout-btn" @click="logout">
        <text class="logout-text">退出</text>
      </view>
    </view>

    <!-- 功能区域 -->
    <view class="content">
      <view class="feature-card" @click="goToChat">
        <view class="card-icon">💬</view>
        <view class="card-content">
          <text class="card-title">智能对话</text>
          <text class="card-desc">与AI助手进行智能对话</text>
        </view>
        <view class="card-arrow">→</view>
      </view>
    </view>
  </view>
</template>

<style scoped>
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 80rpx;
  padding-top: 60rpx;
}

.user-info {
  display: flex;
  flex-direction: column;
}

.welcome {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 8rpx;
}

.app-name {
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 16rpx;
}

.user-name {
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.9);
}

.logout-btn {
  padding: 16rpx 32rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 40rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.logout-text {
  font-size: 28rpx;
  color: #ffffff;
}

.content {
  flex: 1;
}

.feature-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 40rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 32rpx;
}

.feature-card:active {
  opacity: 0.8;
  transform: scale(0.98);
}

.card-icon {
  font-size: 64rpx;
  margin-right: 32rpx;
}

.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.card-title {
  font-size: 36rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 8rpx;
}

.card-desc {
  font-size: 28rpx;
  color: #666666;
}

.card-arrow {
  font-size: 32rpx;
  color: #999999;
}
</style>
